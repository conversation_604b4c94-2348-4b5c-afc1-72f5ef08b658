# JDictSelectTag 默认值功能使用说明

## 概述

JDictSelectTag 组件现已支持默认值功能，可以在组件初始化时自动设置默认值。支持系统字典表（sys_dict_item）和表字典两种类型的默认值处理。

## 新增属性

### 默认值相关属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `autoSetDefault` | `boolean` | `false` | 是否自动设置默认值 |
| `defaultField` | `string` | - | 表字典默认值字段名（如：`is_default`、`default_flag`） |
| `defaultIndex` | `number` | - | 表字典默认值索引（从0开始） |
| `defaultValue` | `string \| string[]` | - | 指定的默认值 |
| `onlySetWhenEmpty` | `boolean` | `true` | 仅在值为空时设置默认值 |

## 使用方式

### 1. 系统字典表默认值

对于系统字典表（sys_dict_item），组件会自动查找 `default_flag = 1` 的项作为默认值。

```vue
<template>
  <!-- 单选模式 -->
  <JDictSelectTag
    v-model:value="gender"
    dictCode="sex"
    :autoSetDefault="true"
    placeholder="请选择性别"
  />
  
  <!-- Radio模式 -->
  <JDictSelectTag
    v-model:value="status"
    dictCode="user_status"
    type="radio"
    :autoSetDefault="true"
  />
  
  <!-- 多选模式 -->
  <JDictSelectTag
    v-model:value="roles"
    dictCode="user_role"
    mode="multiple"
    :autoSetDefault="true"
  />
</template>
```

### 2. 表字典默认值

#### 2.1 通过字段名指定默认值

```vue
<template>
  <JDictSelectTag
    v-model:value="userId"
    dictCode="sys_user,realname,id"
    :autoSetDefault="true"
    defaultField="is_default"
    placeholder="请选择用户"
  />
</template>
```

#### 2.2 通过索引指定默认值

```vue
<template>
  <!-- 选择第一项作为默认值 -->
  <JDictSelectTag
    v-model:value="deptId"
    dictCode="sys_depart,depart_name,id"
    :autoSetDefault="true"
    :defaultIndex="0"
    placeholder="请选择部门"
  />
</template>
```

#### 2.3 通过指定值设置默认值

```vue
<template>
  <!-- 单个默认值 -->
  <JDictSelectTag
    v-model:value="userId"
    dictCode="sys_user,realname,id"
    :autoSetDefault="true"
    defaultValue="admin"
    placeholder="请选择用户"
  />
  
  <!-- 多个默认值（多选模式） -->
  <JDictSelectTag
    v-model:value="userIds"
    dictCode="sys_user,realname,id"
    mode="multiple"
    :autoSetDefault="true"
    :defaultValue="['admin', 'user1']"
    placeholder="请选择用户"
  />
</template>
```

### 3. 控制默认值设置时机

```vue
<template>
  <!-- 仅在值为空时设置默认值（默认行为） -->
  <JDictSelectTag
    v-model:value="status"
    dictCode="user_status"
    :autoSetDefault="true"
    :onlySetWhenEmpty="true"
  />
  
  <!-- 总是设置默认值（会覆盖现有值） -->
  <JDictSelectTag
    v-model:value="status"
    dictCode="user_status"
    :autoSetDefault="true"
    :onlySetWhenEmpty="false"
  />
</template>
```

## 默认值优先级

当同时配置多个默认值选项时，优先级如下：

1. **指定的默认值** (`defaultValue`) - 最高优先级
2. **系统字典表的 defaultFlag** - 对于 sys_dict_item 表
3. **表字典的字段名** (`defaultField`) - 对于表字典
4. **表字典的索引** (`defaultIndex`) - 对于表字典，最低优先级

## 数据库配置

### 系统字典表配置

在 `sys_dict_item` 表中设置 `default_flag` 字段：

```sql
-- 设置某个字典项为默认值
UPDATE sys_dict_item 
SET default_flag = 1 
WHERE dict_id = (SELECT id FROM sys_dict WHERE dict_code = 'sex') 
  AND item_value = '1';

-- 清除其他项的默认值标识
UPDATE sys_dict_item 
SET default_flag = 0 
WHERE dict_id = (SELECT id FROM sys_dict WHERE dict_code = 'sex') 
  AND item_value != '1';
```

### 表字典配置

在相应的业务表中添加默认值字段：

```sql
-- 为用户表添加默认值字段
ALTER TABLE sys_user ADD COLUMN is_default TINYINT(1) DEFAULT 0;

-- 设置默认用户
UPDATE sys_user SET is_default = 1 WHERE username = 'admin';
```

## 注意事项

1. **唯一性约束**：建议每个字典分组只设置一个默认值项（单选模式）
2. **性能考虑**：启用默认值功能会在组件初始化时进行额外的数据处理
3. **缓存影响**：系统字典的默认值会受到字典缓存的影响，修改后可能需要刷新缓存
4. **表字典限制**：表字典的默认值字段查询不会使用缓存，每次都会查询数据库

## 完整示例

```vue
<template>
  <a-form :model="formData" layout="vertical">
    <!-- 系统字典默认值 -->
    <a-form-item label="性别">
      <JDictSelectTag
        v-model:value="formData.gender"
        dictCode="sex"
        :autoSetDefault="true"
        placeholder="请选择性别"
      />
    </a-form-item>
    
    <!-- 表字典默认值 -->
    <a-form-item label="所属部门">
      <JDictSelectTag
        v-model:value="formData.deptId"
        dictCode="sys_depart,depart_name,id"
        :autoSetDefault="true"
        defaultField="is_default"
        placeholder="请选择部门"
      />
    </a-form-item>
    
    <!-- 多选默认值 -->
    <a-form-item label="用户角色">
      <JDictSelectTag
        v-model:value="formData.roleIds"
        dictCode="sys_role,role_name,id"
        mode="multiple"
        :autoSetDefault="true"
        :defaultValue="['user']"
        placeholder="请选择角色"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { reactive } from 'vue';

const formData = reactive({
  gender: null,
  deptId: null,
  roleIds: []
});
</script>
```

## TypeScript 类型定义

```typescript
interface JDictSelectTagProps {
  // 现有属性...
  value?: string | number | string[] | number[];
  dictCode?: string;
  type?: 'select' | 'radio' | 'radioButton';
  mode?: 'multiple';
  
  // 默认值相关属性
  autoSetDefault?: boolean;        // 是否自动设置默认值
  defaultField?: string;           // 表字典默认值字段名
  defaultIndex?: number;           // 表字典默认值索引
  defaultValue?: string | string[]; // 指定的默认值
  onlySetWhenEmpty?: boolean;      // 仅在值为空时设置默认值
}
```
