package org.jeecg.modules.comInterface.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 企业端批量添加单位预约数据DTO
 * @Author: system
 * @Date: 2025-01-08
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CompanyRegBatchCreateDTO", description = "企业端批量添加单位预约数据DTO")
public class CompanyRegBatchCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 团检登记信息
     */
    @Valid
    @ApiModelProperty(value = "团检登记信息", required = true)
    private PlanInfoDTO planInfo;

    /**
     * 团检登记信息
     */
    @Data
    @ApiModel(value = "PlanInfoDTO", description = "团检登记信息")
    public static class PlanInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "ID")
        private String id;

        @ApiModelProperty(value = "预约名称")
        private String regName;

        @ApiModelProperty(value = "单位ID")
        private String companyId;

        @ApiModelProperty(value = "单位名称")
        private String companyName;

        @ApiModelProperty(value = "人员数量")
        private Integer personCount;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @ApiModelProperty(value = "开始体检日期")
        private Date startCheckDate;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @ApiModelProperty(value = "结束体检日期")
        private Date endCheckDate;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "联系人")
        private String linkMan;

        @ApiModelProperty(value = "锁定状态")
        private Integer lockStatus;

        @ApiModelProperty(value = "创建人")
        private String createBy;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

        @ApiModelProperty(value = "更新人")
        private String updateBy;

        @ApiModelProperty(value = "来源")
        private String source;

        private String extra;

        @NotEmpty(message = "分组信息不能为空")
        @Valid
        @ApiModelProperty(value = "分组信息列表", required = true)
        private List<TeamsInfoDTO> teamsInfo;
    }

    /**
     * 分组信息
     */
    @Data
    @ApiModel(value = "TeamsInfoDTO", description = "分组信息")
    public static class TeamsInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "ID")
        private String id;

        @ApiModelProperty(value = "分组编号")
        private String teamNum;

        @ApiModelProperty(value = "团检预约ID")
        private String companyRegId;

        @ApiModelProperty(value = "分组名称")
        private String name;

        @ApiModelProperty(value = "体检类别")
        private String examCategory;

        @ApiModelProperty(value = "付费类型")
        private String payerType;

        @ApiModelProperty(value = "加项付费类型")
        private String addItemPayerType;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "性别限制")
        private String sexLimit;

        @ApiModelProperty(value = "最小年龄")
        private Integer minAge;

        @ApiModelProperty(value = "最大年龄")
        private Integer maxAge;

        @ApiModelProperty(value = "婚姻状况")
        private String maritalStatus;

        @ApiModelProperty(value = "锁定状态")
        private Integer lockStatus;

        @ApiModelProperty(value = "允许共享")
        private String permitShare;

        @ApiModelProperty(value = "允许同步调整限制")
        private String allowSyncAdjustLimit;

        @ApiModelProperty(value = "限额过期")
        private String limitAmountExpired;

        @ApiModelProperty(value = "创建人")
        private String createTy;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        @ApiModelProperty(value = "更新人")
        private String updateBy;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

        @NotEmpty(message = "体检人员信息不能为空")
        @Valid
        @ApiModelProperty(value = "体检人员信息列表", required = true)
        private List<ExaminedPersonnelInfoDTO> examinedPersonnelInfo;
    }

    /**
     * 体检人员信息
     */
    @Data
    @ApiModel(value = "ExaminedPersonnelInfoDTO", description = "体检人员信息")
    public static class ExaminedPersonnelInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @Valid
        @ApiModelProperty(value = "基本信息", required = true)
        private BasicInfoDTO basicInfo;

        @Valid
        @ApiModelProperty(value = "问卷信息")
        private QuestionnaireInfoDTO questionnaireInfo;
    }

    /**
     * 基本信息
     */
    @Data
    @ApiModel(value = "BasicInfoDTO", description = "基本信息")
    public static class BasicInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "姓名不能为空")
        @ApiModelProperty(value = "姓名", required = true)
        private String actualName;

        @ApiModelProperty(value = "登录名")
        private String loginName;

        @ApiModelProperty(value = "性别")
        private Integer gender;

        @ApiModelProperty(value = "部门ID")
        private Integer departmentId;

        @ApiModelProperty(value = "禁用标志")
        private Boolean disabledFlag;

        @ApiModelProperty(value = "手机号")
        private String phone;

        @ApiModelProperty(value = "邮箱")
        private String email;

        @ApiModelProperty(value = "职位ID")
        private Integer positionId;

        @NotBlank(message = "身份证号不能为空")
        @ApiModelProperty(value = "身份证号", required = true)
        private String idCard;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @ApiModelProperty(value = "出生日期")
        private Date birthDate;

        @ApiModelProperty(value = "民族")
        private String nation;

        @ApiModelProperty(value = "婚姻状况")
        private String maritalStatus;

        @ApiModelProperty(value = "地址")
        private String address;

        @ApiModelProperty(value = "街道")
        private String street;

        @ApiModelProperty(value = "省份编码")
        private String provinceCode;

        @ApiModelProperty(value = "省份名称")
        private String provinceName;

        @ApiModelProperty(value = "城市编码")
        private String cityCode;

        @ApiModelProperty(value = "城市名称")
        private String cityName;

        @ApiModelProperty(value = "区县编码")
        private String districtCode;

        @ApiModelProperty(value = "区县名称")
        private String districtName;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "头像")
        private String avatar;

        private String customerRegId;

        private String status;

        @ApiModelProperty(value = "体检项目列表")
        private List<ReItemDTO> reItems;
    }

    /**
     * 体检项目信息
     */
    @Data
    @ApiModel(value = "ReItemDTO", description = "体检项目信息")
    public static class ReItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "项目ID", required = true)
        private String id;

        @ApiModelProperty(value = "项目名称")
        private String name;

        @ApiModelProperty(value = "部位ID")
        private String partId;
    }

    /**
     * 问卷信息
     */
    @Data
    @ApiModel(value = "QuestionnaireInfoDTO", description = "问卷信息")
    public static class QuestionnaireInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "职业史")
        private List<Object> occupationalHistory;

        @ApiModelProperty(value = "放射史")
        private List<Object> radiationHistory;

        @ApiModelProperty(value = "疾病史")
        private List<Object> diseaseHistory;

        @ApiModelProperty(value = "家族史")
        private List<Object> familyHistory;

        @ApiModelProperty(value = "婚姻史")
        private List<Object> maritalHistory;

        @ApiModelProperty(value = "症状")
        private List<Object> symptoms;

        @ApiModelProperty(value = "生殖史")
        private List<Object> reproductiveHistory;

        @ApiModelProperty(value = "吸烟饮酒")
        private Object smokingDrinking;

        @ApiModelProperty(value = "月经史")
        private Object menstrualHistory;

        @ApiModelProperty(value = "签名")
        private SignatureDTO signature;
    }

    /**
     * 签名信息
     */
    @Data
    @ApiModel(value = "SignatureDTO", description = "签名信息")
    public static class SignatureDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "签名图片")
        private String signatureImage;

        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "签名时间")
        private Date signatureTime;

        @ApiModelProperty(value = "签名人姓名")
        private String signerName;

        @ApiModelProperty(value = "签名人身份证")
        private String signerIdCard;

        @ApiModelProperty(value = "设备信息")
        private String deviceInfo;

        @ApiModelProperty(value = "IP地址")
        private String ipAddress;

        @ApiModelProperty(value = "用户代理")
        private String userAgent;
    }
}
