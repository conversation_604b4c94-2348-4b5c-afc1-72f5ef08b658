package org.jeecg.modules.comInterface.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 批量操作结果VO
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "BatchResultVO", description = "批量操作结果VO")
public class BatchResultVO<T> implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 登记ID
     */
    @ApiModelProperty(value = "登记ID")
    private String regId;

    /**
     * 总数量
     */
    @ApiModelProperty(value = "总数量")
    private Integer total;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer failureCount;

    /**
     * 成功列表
     */
    @ApiModelProperty(value = "成功列表")
    private List<T> successList;

    /**
     * 失败列表
     */
    @ApiModelProperty(value = "失败列表")
    private List<FailureItem<T>> failureList;

    /**
     * 任务ID（异步处理时使用）
     */
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    /**
     * 处理状态消息
     */
    @ApiModelProperty(value = "处理状态消息")
    private String message;

    private String extra;

    /**
     * 是否异步处理
     */
    @ApiModelProperty(value = "是否异步处理")
    private Boolean isAsync;

    @Data
    @ApiModel(value = "FailureItem", description = "失败项")
    public static class FailureItem<T> implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 失败的数据
         */
        @ApiModelProperty(value = "失败的数据")
        private T data;

        /**
         * 失败原因
         */
        @ApiModelProperty(value = "失败原因")
        private String reason;

        public FailureItem() {}

        public FailureItem(T data, String reason) {
            this.data = data;
            this.reason = reason;
        }
    }
}
