package org.jeecg.excommons;

public class ExApiConstants {
    //建档
    public static final String PATIENT_ADD_PATH = "/datasync/interface/customer/patientInfoRegister";
    //更新档案
    public static final String PATIENT_UPDATE_PATH = "/datasync/interface/customer/patientInfoUpdate";
    //门诊挂号
    public static final String OUTPATIENT_ADD_PATH = "/datasync/interface/customer-reg/outPatientInfoAdd";
    //体检费用推送
    public static final String EXAM_FEE_PUSH_PATH = "/datasync/interface/sendReceiptInfo";
    //体检退费推送
    public static final String EXAM_REFUND_PUSH_PATH = "/datasync/interface/sendRefundInfo";
    //基本信息-同步科室
    public static final String DEPT_SYNC_PATH = "/datasync/interface/sync-department";
    //基本信息-同步项目组合
    public static final String ITEM_GROUP_SYNC_PATH = "/datasync/interface/sync-itemgroup";
    //基本信息-同步人员
    public static final String USER_SYNC_PATH = "/datasync/interface/sync-user";
    //体检卡收费
    public static final String CARD_FEE_PATH = "/datasync/interface/sendReceipt4CardOrder";
    //主动获取检查报告
    public static final String REPORT_CHECK_SYNC_PATH = "/datasync/interface/report/fetchCheckReport";
    //同步LIS数据
    public static final String REPORT_LIS_SYNC_PATH = "/datasync/interface/report/fetchLisData";
    //推送项目信息
    public static final String SEND_ITEM_GROUP_PATH = "/datasync/customerRegItemGroup/sendItemGroupInfo";

    public static final String QUERY_APPLY_STATUS_PATH = "/datasync/interface/getApplyStatus";

    //作废收费申请
    public static final String INVALIDATE_PAY_PUSH_PATH = "/datasync/interface/deleteFeeList";
    //作废退费申请
    public static final String INVALIDATE_REFUND_PUSH_PATH = "/datasync/interface/deleteRefundList";

    // 电子票据相关接口
    //申请电子票据
    public static final String ELECTRONIC_RECEIPT_APPLY_PATH = "/vsaas/receipt/apply";
    //预开电子票据
    public static final String ELECTRONIC_RECEIPT_PRE_ISSUE_PATH = "/vsaas/receipt/preIssue";
    //查询票据状态
    public static final String ELECTRONIC_RECEIPT_QUERY_STATUS_PATH = "/vsaas/receipt/queryStatus";
    //冲红票据
    public static final String ELECTRONIC_RECEIPT_REFUND_PATH = "/vsaas/receipt/refund";
    //作废票据
    public static final String ELECTRONIC_RECEIPT_VOID_PATH = "/vsaas/receipt/void";
    //批量申请票据
    public static final String ELECTRONIC_RECEIPT_BATCH_APPLY_PATH = "/vsaas/receipt/batchApply";
    //下载票据文件
    public static final String ELECTRONIC_RECEIPT_DOWNLOAD_PATH = "/vsaas/receipt/download";
    //同步票据状态
    public static final String ELECTRONIC_RECEIPT_SYNC_STATUS_PATH = "/vsaas/receipt/syncStatus";


    //体检费用推送
    public static final String ONLINE_FEE_PUSH_PATH = "/datasync/interface/createOnlinePayment";
    //体检退费推送
    public static final String ONLINE_REFUND_PUSH_PATH = "/datasync/interface/refundOnlinePayment";
}
