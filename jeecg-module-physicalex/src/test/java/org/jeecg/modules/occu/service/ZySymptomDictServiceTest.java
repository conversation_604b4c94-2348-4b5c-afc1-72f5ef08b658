package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZySymptomDict;
import org.jeecg.modules.occu.vo.ZySymptomSystemVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * @Description: 症状字典服务测试
 * @Author: jeecg-boot
 * @Date: 2024-12-11
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class ZySymptomDictServiceTest {

    @Autowired
    private IZySymptomDictService zySymptomDictService;

    @Test
    public void testGetSymptomsBySystem() {
        // 测试获取按系统分组的症状字典列表
        List<ZySymptomSystemVO> result = zySymptomDictService.getSymptomsBySystem();
        
        System.out.println("系统分组数量: " + result.size());
        
        for (ZySymptomSystemVO systemVO : result) {
            System.out.println("系统名称: " + systemVO.getSystemName());
            System.out.println("排序号: " + systemVO.getSortOrder());
            System.out.println("默认标志: " + systemVO.getDefaultFlag());
            System.out.println("症状数量: " + systemVO.getSymptomList().size());
            System.out.println("---");
        }
    }

    @Test
    public void testClearSystemCache() {
        // 测试缓存清除
        zySymptomDictService.clearSystemCache();
        System.out.println("缓存清除成功");
    }
}
