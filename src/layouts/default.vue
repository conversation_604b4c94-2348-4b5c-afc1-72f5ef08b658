<template>
  <div class="default-layout">
    <header class="layout-header">
      <h1>流式JSON解析演示</h1>
      <div class="nav-links">
        <a href="/">返回首页</a>
      </div>
    </header>
    <main class="layout-content">
      <router-view></router-view>
    </main>
    <footer class="layout-footer">
      <p>流式JSON解析工具演示 &copy; 2023</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 简单布局组件
</script>

<style scoped>
.default-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.layout-header {
  background-color: #1890ff;
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.layout-header h1 {
  margin: 0;
  font-size: 20px;
}

.nav-links a {
  color: white;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.layout-content {
  flex: 1;
  padding: 24px;
  background-color: #f0f2f5;
}

.layout-footer {
  background-color: #001529;
  color: rgba(255, 255, 255, 0.65);
  text-align: center;
  padding: 16px;
}
</style> 