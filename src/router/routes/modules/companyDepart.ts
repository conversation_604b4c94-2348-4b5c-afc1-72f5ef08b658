import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const companyDepart: AppRouteModule = {
  path: '/companyDepart',
  name: 'CompanyDepart',
  component: LAYOUT,
  redirect: '/companyDepart/manage',
  meta: {
    orderNo: 1500,
    icon: 'ant-design:apartment-outlined',
    title: '单位部门管理',
  },
  children: [
    {
      path: 'manage',
      name: 'CompanyDepartManage',
      component: () => import('/@/views/companyDepart/index.vue'),
      meta: {
        title: '单位部门管理',
        icon: 'ant-design:apartment-outlined',
      },
    },
    {
      path: 'test',
      name: 'CompanyDepartTest',
      component: () => import('/@/views/companyDepart/test.vue'),
      meta: {
        title: '功能测试',
        icon: 'ant-design:experiment-outlined',
      },
    },
  ],
};

export default companyDepart;
