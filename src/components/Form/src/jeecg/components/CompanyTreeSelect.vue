<template>
  <a-tree-select
    v-model:value="selectedValue"
    :tree-data="treeData"
    :load-data="loadData"
    :placeholder="loading ? '加载中...' : placeholder"
    :multiple="multiple"
    :allow-clear="allowClear"
    :show-search="showSearch"
    :search-value="searchValue"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :get-popup-container="getPopupContainer"
    tree-node-filter-prop="title"
    :tree-checkable="multiple"
    :tree-check-strictly="checkStrictly"
    :label-in-value="labelInValue"
    :loading="loading"
    :disabled="loading"
    @change="handleChange"
    @search="handleSearch"
    @tree-expand="handleTreeExpand"
    @dropdown-visible-change="handleDropdownVisibleChange"
  >
    <template #title="{ title, key, categoryName }">
      <span>
        {{ title }}
        <span v-if="categoryName" class="company-category">（{{ categoryName }}）</span>
      </span>
    </template>
  </a-tree-select>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { loadTreeData, loadTreeChildren, searchCompany } from '/@/views/basicinfo/Company.api';
import { list as getCompanyCategoryList } from '/@/views/basicinfo/CompanyCategory.api';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

interface TreeNode {
  key: string;
  title: string;
  value: string;
  isLeaf?: boolean;
  children?: TreeNode[];
  categoryId?: string;
  categoryName?: string;
  disabled?: boolean;
}

interface Props {
  value?: string | string[] | any;
  placeholder?: string;
  multiple?: boolean;
  allowClear?: boolean;
  showSearch?: boolean;
  checkStrictly?: boolean;
  labelInValue?: boolean;
  getPopupContainer?: (node: HTMLElement) => HTMLElement;
  // 是否显示分类信息
  showCategory?: boolean;
  // 是否只能选择叶子节点
  leafOnly?: boolean;
  // 过滤条件
  filterParams?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择单位',
  multiple: false,
  allowClear: true,
  showSearch: true,
  checkStrictly: false,
  labelInValue: false,
  showCategory: true,
  leafOnly: false,
});

const emit = defineEmits<{
  change: [value: any, label?: any, extra?: any];
  search: [value: string];
}>();

const selectedValue = ref();
const treeData = ref<TreeNode[]>([]);
const searchValue = ref('');
const loading = ref(false);
const categoryMap = ref<Map<string, string>>(new Map());

// 监听外部value变化
watch(
  () => props.value,
  (newVal) => {
    selectedValue.value = newVal;
  },
  { immediate: true }
);

// 初始化加载数据
onMounted(async () => {
  await loadCategories();
  await loadRootData();
});

/**
 * 加载分类数据
 */
async function loadCategories() {
  if (!props.showCategory) return;
  
  try {
    const res = await getCompanyCategoryList({ pageSize: 1000 });
    if (res.success && res.result?.records) {
      res.result.records.forEach((item: any) => {
        categoryMap.value.set(item.id, item.name);
      });
    }
  } catch (error) {
    console.warn('加载单位分类失败:', error);
  }
}

/**
 * 加载根节点数据
 */
async function loadRootData() {
  try {
    loading.value = true;
    const res = await loadTreeData({ async: true, pcode: '' });
    
    if (res.success && res.result) {
      treeData.value = formatTreeData(res.result);
    }
  } catch (error) {
    console.error('加载单位树数据失败:', error);
    createMessage.error('加载单位数据失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 异步加载子节点数据
 */
async function loadData(treeNode: any): Promise<void> {
  try {
    const res = await loadTreeChildren({ pid: treeNode.key });

    if (res.success && res.result) {
      const children = formatTreeData(res.result);

      // 更新树节点数据
      if (treeNode.dataRef) {
        treeNode.dataRef.children = children;
      }

      // 触发树组件重新渲染
      treeData.value = [...treeData.value];
    } else {
      // 如果没有子节点，标记为叶子节点
      if (treeNode.dataRef) {
        treeNode.dataRef.isLeaf = true;
      }
    }
  } catch (error) {
    console.error('加载子节点数据失败:', error);
    createMessage.error('加载子节点数据失败');

    // 出错时也标记为叶子节点，避免无限加载
    if (treeNode.dataRef) {
      treeNode.dataRef.isLeaf = true;
    }
  }
}

/**
 * 格式化树形数据
 */
function formatTreeData(data: any[]): TreeNode[] {
  return data.map((item) => {
    const node: TreeNode = {
      key: item.key || item.id,
      title: item.title || item.name,
      value: item.key || item.id,
      isLeaf: item.isLeaf === true || item.isLeaf === 1,
      categoryId: item.categoryId,
      categoryName: item.categoryId ? categoryMap.value.get(item.categoryId) : undefined,
    };

    // 如果只能选择叶子节点，非叶子节点设为禁用
    if (props.leafOnly && !node.isLeaf) {
      node.disabled = true;
    }

    return node;
  });
}

/**
 * 处理选择变化
 */
function handleChange(value: any, label?: any, extra?: any) {
  selectedValue.value = value;
  emit('change', value, label, extra);
}

/**
 * 处理搜索
 */
function handleSearch(value: string) {
  searchValue.value = value;
  emit('search', value);
  
  // 如果有搜索值，执行搜索
  if (value && value.trim()) {
    performSearch(value.trim());
  } else {
    // 清空搜索时重新加载根数据
    loadRootData();
  }
}

/**
 * 执行搜索
 */
async function performSearch(keyword: string) {
  try {
    loading.value = true;

    // 暂时使用现有的loadTreeData接口进行搜索
    // 后续可以扩展专门的搜索接口
    const res = await loadTreeData({
      async: false,
      pcode: '',
      keyword: keyword,
      ...props.filterParams
    });

    if (res.success && res.result) {
      // 过滤包含关键词的节点
      const filteredData = filterTreeByKeyword(res.result, keyword);
      treeData.value = formatTreeData(filteredData);
    } else {
      treeData.value = [];
    }
  } catch (error) {
    console.error('搜索单位失败:', error);
    createMessage.error('搜索失败');
    treeData.value = [];
  } finally {
    loading.value = false;
  }
}

/**
 * 根据关键词过滤树形数据
 */
function filterTreeByKeyword(data: any[], keyword: string): any[] {
  const result: any[] = [];

  for (const item of data) {
    const title = item.title || item.name || '';
    const matchesKeyword = title.toLowerCase().includes(keyword.toLowerCase());

    let children: any[] = [];
    if (item.children && item.children.length > 0) {
      children = filterTreeByKeyword(item.children, keyword);
    }

    // 如果当前节点匹配或有匹配的子节点，则包含此节点
    if (matchesKeyword || children.length > 0) {
      result.push({
        ...item,
        children: children.length > 0 ? children : undefined,
        isLeaf: children.length === 0 && (item.isLeaf === true || item.isLeaf === 1)
      });
    }
  }

  return result;
}

/**
 * 处理树节点展开
 */
function handleTreeExpand(expandedKeys: string[]) {
  // 可以在这里处理展开逻辑
}

/**
 * 处理下拉框显示状态变化
 */
function handleDropdownVisibleChange(visible: boolean) {
  if (visible && treeData.value.length === 0) {
    loadRootData();
  }
}
</script>

<style scoped>
.company-category {
  color: #999;
  font-size: 12px;
}
</style>
