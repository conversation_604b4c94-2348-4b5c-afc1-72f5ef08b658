# 单位选择组件 (JSelectCompany)

基于现有的Company和CompanyCategory实体类，创建的支持树形结构的单位下拉选择器组件。

## 功能特性

- ✅ 树形结构展示单位层级关系
- ✅ 支持单选和多选模式
- ✅ 实时搜索功能
- ✅ 异步加载子节点
- ✅ 显示单位分类信息
- ✅ 支持只选择叶子节点
- ✅ 支持返回标签值对象
- ✅ 支持过滤条件
- ✅ 响应式设计

## 组件结构

```
JSelectCompany.vue          # 封装的选择器组件（对外使用）
├── CompanyTreeSelect.vue   # 核心树形选择组件
└── Company.api.ts          # 扩展的API接口
```

## 基础用法

### 单选模式
```vue
<template>
  <JSelectCompany
    v-model:value="companyId"
    placeholder="请选择单位"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import JSelectCompany from '/@/components/Form/src/jeecg/components/JSelectCompany.vue';

const companyId = ref();

function handleChange(value, label, extra) {
  console.log('选择的单位:', { value, label, extra });
}
</script>
```

### 多选模式
```vue
<template>
  <JSelectCompany
    v-model:value="companyIds"
    :multiple="true"
    placeholder="请选择多个单位"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';

const companyIds = ref([]);
</script>
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `value` | `string \| string[] \| any` | - | 选中的值 |
| `placeholder` | `string` | `'请选择单位'` | 占位符文本 |
| `multiple` | `boolean` | `false` | 是否多选 |
| `allowClear` | `boolean` | `true` | 是否允许清空 |
| `showSearch` | `boolean` | `true` | 是否显示搜索框 |
| `checkStrictly` | `boolean` | `false` | 多选时是否严格模式 |
| `labelInValue` | `boolean` | `false` | 是否返回标签值对象 |
| `showCategory` | `boolean` | `true` | 是否显示分类信息 |
| `leafOnly` | `boolean` | `false` | 是否只能选择叶子节点 |
| `filterParams` | `Record<string, any>` | - | 过滤条件 |
| `getPopupContainer` | `Function` | - | 获取弹出容器的函数 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `change` | `(value, label, extra)` | 选择变化时触发 |
| `search` | `(value)` | 搜索时触发 |
| `select` | `(value, node)` | 选中时触发 |

## 高级用法

### 只选择叶子节点
```vue
<JSelectCompany
  v-model:value="leafCompanyId"
  :leaf-only="true"
  placeholder="只能选择最底层单位"
/>
```

### 返回标签值对象
```vue
<JSelectCompany
  v-model:value="companyInfo"
  :label-in-value="true"
  placeholder="返回完整信息"
/>
<!-- 返回格式: { value: 'id', label: '单位名称' } -->
```

### 不显示分类信息
```vue
<JSelectCompany
  v-model:value="companyId"
  :show-category="false"
  placeholder="不显示分类"
/>
```

### 带过滤条件
```vue
<JSelectCompany
  v-model:value="companyId"
  :filter-params="{ categoryId: 'specific-category' }"
  placeholder="仅显示特定分类"
/>
```

## 在表单中使用

### 基础表单
```vue
<template>
  <a-form :model="form" :rules="rules">
    <a-form-item label="所属单位" name="companyId">
      <JSelectCompany
        v-model:value="form.companyId"
        :leaf-only="true"
        placeholder="请选择所属单位"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
const form = reactive({
  companyId: undefined
});

const rules = {
  companyId: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ]
};
</script>
```

### 多选表单
```vue
<a-form-item label="管理单位" name="managedCompanies">
  <JSelectCompany
    v-model:value="form.managedCompanies"
    :multiple="true"
    placeholder="请选择管理的单位"
  />
</a-form-item>
```

## 样式定制

组件支持通过CSS变量进行样式定制：

```css
.JSelectCompany {
  /* 自定义宽度 */
  width: 100%;
}

/* 分类信息样式 */
.company-category {
  color: #999;
  font-size: 12px;
}
```

## 注意事项

1. **后端接口依赖**: 组件依赖后端的Company相关接口，确保以下接口可用：
   - `/basicinfo/company/loadTreeRoot` - 加载根节点
   - `/basicinfo/company/loadTreeChildren` - 加载子节点
   - `/basicinfo/companyCategory/list` - 加载分类信息

2. **异步加载**: 组件支持异步加载子节点，大数据量时性能更好

3. **搜索功能**: 当前搜索是前端过滤实现，如需后端搜索可扩展API

4. **数据格式**: 确保后端返回的数据格式符合TreeSelect组件要求

## 测试页面

项目中提供了测试页面：
- `src/views/test/CompanySelectTest.vue` - 组件功能测试
- `src/views/test/CompanyFormExample.vue` - 表单使用示例

## 更新日志

- v1.0.0 - 初始版本，支持基础的树形选择功能
- 支持单选/多选模式
- 支持搜索和异步加载
- 支持分类信息显示
