<template>
  <a-spin :spinning="loading">
    <a-form ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="部门名称" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入部门名称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="部门简称" name="shortName">
            <a-input v-model:value="formData.shortName" placeholder="请输入部门简称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="部门编码" name="orgCode">
            <a-input v-model:value="formData.orgCode" placeholder="请输入部门编码" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="助记码" name="helpChar">
            <a-input v-model:value="formData.helpChar" placeholder="请输入助记码" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系电话" name="telephone">
            <a-input v-model:value="formData.telephone" placeholder="请输入联系电话" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="memo">
            <a-textarea v-model:value="formData.memo" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider />

      <div style="text-align: right">
        <a-space>
          <a-button @click="onReset">重置</a-button>
          <a-button type="primary" @click="onSubmit">保存</a-button>
        </a-space>
      </div>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, watch, reactive } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { saveOrUpdateDict } from '../Company.api';

  const props = defineProps({
    data: { type: Object, default: () => ({}) },
    companyId: { type: String, required: true },
  });

  const emit = defineEmits(['success']);
  const { createMessage } = useMessage();

  const loading = ref<boolean>(false);
  const formRef = ref();
  const isUpdate = ref<boolean>(false);

  // 表单数据
  const formData = reactive({
    id: '',
    orgType: '2', // 部门类型
    pid: '',
    name: '',
    shortName: '',
    orgCode: '',
    helpChar: '',
    telephone: '',
    memo: '',
  });

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: '请输入部门名称', trigger: 'blur' },
      { max: 100, message: '名称长度不能超过100个字符', trigger: 'blur' },
    ],
    orgCode: [{ max: 50, message: '编码长度不能超过50个字符', trigger: 'blur' }],
    telephone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  };

  // 监听数据变化
  watch(
    () => props.data,
    (newData) => {
      console.log('CompanyDepartForm 接收到的数据:', newData); // 调试日志
      if (newData && Object.keys(newData).length > 0) {
        isUpdate.value = !!newData.id;
        const formValues = {
          id: newData.id || '',
          orgType: '2', // 部门类型
          pid: newData.pid || props.companyId,
          name: newData.name || '',
          shortName: newData.shortName || '',
          orgCode: newData.orgCode || '',
          helpChar: newData.helpChar || '',
          telephone: newData.telephone || '',
          memo: newData.memo || '',
        };
        console.log('设置表单数据:', formValues); // 调试日志
        Object.assign(formData, formValues);
      } else {
        // 新增模式
        isUpdate.value = false;
        Object.assign(formData, {
          id: '',
          orgType: '2', // 部门类型
          pid: props.companyId,
          name: '',
          shortName: '',
          orgCode: '',
          helpChar: '',
          telephone: '',
          memo: '',
        });
      }
    },
    { immediate: true, deep: true }
  );

  // 重置表单
  function onReset() {
    formRef.value?.resetFields();
  }

  // 提交表单
  async function onSubmit() {
    try {
      await formRef.value?.validate();
      loading.value = true;

      // 数据处理
      const submitData = { ...formData };
      if (!submitData.pid) {
        submitData.pid = props.companyId;
      }
      submitData.hasChild = '0'; // 新增部门默认无子节点

      // 提交保存
      await saveOrUpdateDict(submitData, isUpdate.value);
      createMessage.success(isUpdate.value ? '更新成功' : '保存成功');

      // 触发成功回调
      emit('success');
    } catch (error) {
      console.error('保存失败:', error);
      if (error.errorFields) {
        // 表单验证失败
        createMessage.error('请检查表单填写是否正确');
      } else {
        createMessage.error('保存失败');
      }
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="less" scoped>
  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
</style>
