<template>
  <div class="company-depart-management">
    <a-row :gutter="10" style="height: 500px">
      <!-- 左侧部门树 -->
      <a-col :span="8">
        <a-card :bordered="false" style="height: 100%">
          <template #title>
            <a-space>
              <Icon icon="ant-design:apartment-outlined" />
              <span>部门结构</span>
            </a-space>
          </template>
          <template #extra>
            <a-space>
              <a-button type="primary" size="small" @click="onAddDepart">
                <Icon icon="ant-design:plus-outlined" />
                新增部门
              </a-button>
            </a-space>
          </template>
          
          <div style="height: 400px; overflow: auto">
            <a-spin :spinning="treeLoading">
              <a-input-search 
                placeholder="搜索部门..." 
                style="margin-bottom: 10px" 
                @search="onSearch" 
                allow-clear
              />
              
              <template v-if="treeData.length > 0">
                <a-tree
                  v-if="!treeReloading"
                  :treeData="treeData"
                  :selectedKeys="selectedKeys"
                  :expandedKeys="expandedKeys"
                  :load-data="loadChildrenTreeData"
                  @select="onTreeSelect"
                  @expand="onTreeExpand"
                >
                  <template #title="{ key: treeKey, title, dataRef }">
                    <a-dropdown :trigger="['contextmenu']">
                      <span>
                        <Icon 
                          icon="ant-design:apartment-outlined" 
                          style="margin-right: 5px; color: #52c41a" 
                        />
                        {{ title }}
                      </span>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item key="1" @click="onAddChildDepart(dataRef)">
                            <Icon icon="ant-design:plus-outlined" />
                            添加子部门
                          </a-menu-item>
                          <a-menu-item key="2" @click="onEditDepart(dataRef)">
                            <Icon icon="ant-design:edit-outlined" />
                            编辑部门
                          </a-menu-item>
                          <a-menu-item key="3" @click="onDeleteDepart(dataRef)" style="color: red">
                            <Icon icon="ant-design:delete-outlined" />
                            删除部门
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </template>
                </a-tree>
              </template>
              <a-empty v-else description="暂无部门数据" />
            </a-spin>
          </div>
        </a-card>
      </a-col>
      
      <!-- 右侧部门详情 -->
      <a-col :span="16">
        <a-card :bordered="false" style="height: 100%">
          <template #title>
            <a-space>
              <Icon icon="ant-design:form-outlined" />
              <span>{{ currentDepart?.name || '部门信息' }}</span>
            </a-space>
          </template>
          
          <div style="height: 400px; overflow: auto">
            <template v-if="currentDepart">
              <CompanyDepartForm
                :data="currentDepart"
                :companyId="companyId"
                @success="onDepartFormSuccess"
              />
            </template>
            <a-empty v-else description="请从左侧选择部门进行管理" />
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 部门表单弹窗 -->
    <CompanyDepartModal 
      ref="departModalRef" 
      :companyId="companyId"
      @success="onModalSuccess" 
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import CompanyDepartForm from './CompanyDepartForm.vue';
  import CompanyDepartModal from './CompanyDepartModal.vue';
  import { 
    queryCompanyDepartTreeSync, 
    deleteCompanyDepart,
    searchCompanyDepartByKeywords 
  } from '../Company.api';

  const props = defineProps({
    companyId: { type: String, required: true },
  });

  const emit = defineEmits(['success']);
  const { createMessage, createConfirm } = useMessage();

  // 树相关状态
  const treeLoading = ref<boolean>(false);
  const treeReloading = ref<boolean>(false);
  const treeData = ref<any[]>([]);
  const expandedKeys = ref<any[]>([]);
  const selectedKeys = ref<any[]>([]);
  const currentDepart = ref<any>(null);
  const departModalRef = ref();

  // 初始化加载数据
  onMounted(() => {
    loadDepartTreeData();
  });

  // 加载部门树数据
  async function loadDepartTreeData() {
    if (!props.companyId) return;
    
    try {
      treeLoading.value = true;
      // 加载指定单位下的部门数据
      const result = await queryCompanyDepartTreeSync({ 
        pid: props.companyId,
        orgType: '2' // 只加载部门类型
      });
      if (Array.isArray(result)) {
        treeData.value = result;
      }
    } catch (error) {
      console.error('加载部门数据失败:', error);
      createMessage.error('加载部门数据失败');
    } finally {
      treeLoading.value = false;
    }
  }

  // 异步加载子节点数据
  async function loadChildrenTreeData(treeNode) {
    try {
      const result = await queryCompanyDepartTreeSync({ 
        pid: treeNode.dataRef.id,
        orgType: '2'
      });
      if (Array.isArray(result)) {
        treeNode.dataRef.children = result;
      }
    } catch (error) {
      console.error('加载子节点失败:', error);
    }
  }

  // 树节点选择
  function onTreeSelect(selectedKeys: any[], { selected, selectedNodes }) {
    if (selected && selectedNodes.length > 0) {
      const selectedNode = selectedNodes[0];
      // 获取节点的实际数据，可能在 dataRef 中或者直接在节点属性中
      const nodeData = selectedNode.dataRef || selectedNode;
      console.log('选中的节点数据:', nodeData); // 调试日志

      // 将树节点数据映射为表单期望的数据结构
      const mappedData = {
        id: nodeData.key || nodeData.id,
        name: nodeData.title || nodeData.name,
        shortName: nodeData.shortName || '',
        orgCode: nodeData.orgCode || '',
        helpChar: nodeData.helpChar || '',
        telephone: nodeData.telephone || '',
        memo: nodeData.memo || '',
        pid: nodeData.parentId || nodeData.pid,
        orgType: '2', // 部门类型
        // 保留原始数据以备后用
        ...nodeData
      };

      console.log('映射后的数据:', mappedData); // 调试日志
      currentDepart.value = mappedData;
    } else {
      currentDepart.value = null;
    }
  }

  // 树节点展开
  function onTreeExpand(expandedKeys: any[]) {
    expandedKeys.value = expandedKeys;
  }

  // 搜索部门
  async function onSearch(value: string) {
    if (!value.trim()) {
      loadDepartTreeData();
      return;
    }
    
    try {
      treeLoading.value = true;
      const result = await searchCompanyDepartByKeywords({ 
        keyWord: value,
        pid: props.companyId,
        orgType: '2'
      });
      if (Array.isArray(result)) {
        treeData.value = result;
        // 展开所有搜索结果
        expandedKeys.value = getAllKeys(result);
      }
    } catch (error) {
      console.error('搜索失败:', error);
    } finally {
      treeLoading.value = false;
    }
  }

  // 获取所有节点key
  function getAllKeys(treeData: any[], keys: any[] = []): any[] {
    treeData.forEach((item) => {
      keys.push(item.key || item.id);
      if (item.children && item.children.length > 0) {
        getAllKeys(item.children, keys);
      }
    });
    return keys;
  }

  // 新增部门
  function onAddDepart() {
    if (!props.companyId) {
      createMessage.warning('请先保存单位信息');
      return;
    }
    departModalRef.value?.add({
      pid: props.companyId,
      orgType: '2'
    });
  }

  // 新增子部门
  function onAddChildDepart(parentDepart: any) {
    departModalRef.value?.add({
      pid: parentDepart.id,
      orgType: '2'
    });
  }

  // 编辑部门
  function onEditDepart(depart: any) {
    departModalRef.value?.edit(depart);
  }

  // 删除部门
  function onDeleteDepart(depart: any) {
    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `确定要删除部门"${depart.name}"吗？`,
      onOk: async () => {
        try {
          await deleteCompanyDepart(depart.id);
          createMessage.success('删除成功');
          await loadDepartTreeData();
          if (currentDepart.value?.id === depart.id) {
            currentDepart.value = null;
          }
        } catch (error) {
          console.error('删除失败:', error);
          createMessage.error('删除失败');
        }
      }
    });
  }

  // 部门表单成功回调
  function onDepartFormSuccess() {
    createMessage.success('保存成功');
    loadDepartTreeData();
    emit('success');
  }

  // 弹窗成功回调
  function onModalSuccess() {
    loadDepartTreeData();
    emit('success');
  }

  // 暴露方法给父组件
  defineExpose({
    loadDepartTreeData,
  });
</script>

<style lang="less" scoped>
  .company-depart-management {
    padding: 10px;
    
    .ant-card {
      .ant-card-head {
        min-height: 40px;
        padding: 0 12px;
        
        .ant-card-head-title {
          padding: 8px 0;
          font-size: 14px;
        }
        
        .ant-card-extra {
          padding: 8px 0;
        }
      }
      
      .ant-card-body {
        padding: 12px;
      }
    }
  }
</style>
