<template>
  <a-modal
    :title="title"
    width="60%"
    :open="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
    cancelText="关闭"
  >
    <CompanyDepartForm 
      ref="departFormRef" 
      :data="currentData" 
      :companyId="companyId"
      @success="onFormSuccess" 
    />
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick } from 'vue';
  import CompanyDepartForm from './CompanyDepartForm.vue';

  const props = defineProps({
    companyId: { type: String, required: true },
  });

  const emit = defineEmits(['success']);

  const title = ref<string>('');
  const visible = ref<boolean>(false);
  const confirmLoading = ref<boolean>(false);
  const departFormRef = ref();
  const currentData = ref<any>({});
  const isUpdate = ref<boolean>(false);

  /**
   * 新增部门
   */
  function add(obj = {}) {
    title.value = '新增部门';
    isUpdate.value = false;
    currentData.value = {
      orgType: '部门',
      pid: props.companyId,
      ...obj
    };
    visible.value = true;
  }

  /**
   * 编辑部门
   */
  function edit(record) {
    title.value = '编辑部门';
    isUpdate.value = true;
    currentData.value = { ...record };
    visible.value = true;
  }

  /**
   * 确定按钮点击事件
   */
  async function handleOk() {
    if (departFormRef.value) {
      await departFormRef.value.onSubmit();
    }
  }

  /**
   * 表单保存成功回调
   */
  function onFormSuccess() {
    handleCancel();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
    currentData.value = {};
  }

  defineExpose({
    add,
    edit,
  });
</script>
