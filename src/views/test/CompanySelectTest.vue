<template>
  <div class="company-select-test">
    <a-card title="单位选择组件测试">
      <a-space direction="vertical" size="large" style="width: 100%">
        
        <!-- 基础单选 -->
        <div>
          <h3>基础单选</h3>
          <JSelectCompany
            v-model:value="singleValue"
            placeholder="请选择单位"
            @change="handleSingleChange"
            @select="handleSingleSelect"
          />
          <div class="result-display">
            <strong>选中值：</strong>{{ singleValue || '无' }}
          </div>
        </div>

        <!-- 多选模式 -->
        <div>
          <h3>多选模式</h3>
          <JSelectCompany
            v-model:value="multipleValue"
            :multiple="true"
            placeholder="请选择多个单位"
            @change="handleMultipleChange"
          />
          <div class="result-display">
            <strong>选中值：</strong>{{ JSON.stringify(multipleValue) || '无' }}
          </div>
        </div>

        <!-- 返回标签值对象 -->
        <div>
          <h3>返回标签值对象</h3>
          <JSelectCompany
            v-model:value="labelValue"
            :label-in-value="true"
            placeholder="请选择单位（返回对象）"
            @change="handleLabelChange"
          />
          <div class="result-display">
            <strong>选中值：</strong>{{ JSON.stringify(labelValue) || '无' }}
          </div>
        </div>

        <!-- 只能选择叶子节点 -->
        <div>
          <h3>只能选择叶子节点</h3>
          <JSelectCompany
            v-model:value="leafValue"
            :leaf-only="true"
            placeholder="只能选择最底层单位"
            @change="handleLeafChange"
          />
          <div class="result-display">
            <strong>选中值：</strong>{{ leafValue || '无' }}
          </div>
        </div>

        <!-- 不显示分类信息 -->
        <div>
          <h3>不显示分类信息</h3>
          <JSelectCompany
            v-model:value="noCategoryValue"
            :show-category="false"
            placeholder="不显示分类信息"
            @change="handleNoCategoryChange"
          />
          <div class="result-display">
            <strong>选中值：</strong>{{ noCategoryValue || '无' }}
          </div>
        </div>

        <!-- 带过滤条件 -->
        <div>
          <h3>带过滤条件（仅显示特定分类）</h3>
          <JSelectCompany
            v-model:value="filteredValue"
            :filter-params="{ categoryId: 'specific-category-id' }"
            placeholder="仅显示特定分类的单位"
            @change="handleFilteredChange"
          />
          <div class="result-display">
            <strong>选中值：</strong>{{ filteredValue || '无' }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div>
          <a-space>
            <a-button @click="clearAll">清空所有选择</a-button>
            <a-button @click="setDefaultValues">设置默认值</a-button>
            <a-button @click="showAllValues">显示所有值</a-button>
          </a-space>
        </div>

        <!-- 所有值显示 -->
        <div v-if="showValues">
          <h3>所有选中值</h3>
          <pre>{{ JSON.stringify(allValues, null, 2) }}</pre>
        </div>

      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import JSelectCompany from '/@/components/Form/src/jeecg/components/JSelectCompany.vue';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

// 各种模式的选中值
const singleValue = ref();
const multipleValue = ref([]);
const labelValue = ref();
const leafValue = ref();
const noCategoryValue = ref();
const filteredValue = ref();
const showValues = ref(false);

// 计算所有值
const allValues = computed(() => ({
  singleValue: singleValue.value,
  multipleValue: multipleValue.value,
  labelValue: labelValue.value,
  leafValue: leafValue.value,
  noCategoryValue: noCategoryValue.value,
  filteredValue: filteredValue.value,
}));

// 事件处理函数
function handleSingleChange(value: any, label?: any, extra?: any) {
  console.log('单选变化:', { value, label, extra });
  createMessage.info(`单选选择: ${value}`);
}

function handleSingleSelect(value: any, node?: any) {
  console.log('单选选中:', { value, node });
}

function handleMultipleChange(value: any, label?: any, extra?: any) {
  console.log('多选变化:', { value, label, extra });
  createMessage.info(`多选选择: ${Array.isArray(value) ? value.join(', ') : value}`);
}

function handleLabelChange(value: any, label?: any, extra?: any) {
  console.log('标签值变化:', { value, label, extra });
  createMessage.info(`标签值选择: ${JSON.stringify(value)}`);
}

function handleLeafChange(value: any, label?: any, extra?: any) {
  console.log('叶子节点变化:', { value, label, extra });
  createMessage.info(`叶子节点选择: ${value}`);
}

function handleNoCategoryChange(value: any, label?: any, extra?: any) {
  console.log('无分类变化:', { value, label, extra });
  createMessage.info(`无分类选择: ${value}`);
}

function handleFilteredChange(value: any, label?: any, extra?: any) {
  console.log('过滤变化:', { value, label, extra });
  createMessage.info(`过滤选择: ${value}`);
}

// 操作函数
function clearAll() {
  singleValue.value = undefined;
  multipleValue.value = [];
  labelValue.value = undefined;
  leafValue.value = undefined;
  noCategoryValue.value = undefined;
  filteredValue.value = undefined;
  createMessage.success('已清空所有选择');
}

function setDefaultValues() {
  // 这里需要根据实际的单位ID来设置
  singleValue.value = 'company-id-1';
  multipleValue.value = ['company-id-1', 'company-id-2'];
  labelValue.value = { value: 'company-id-1', label: '示例单位' };
  leafValue.value = 'leaf-company-id';
  noCategoryValue.value = 'company-id-3';
  filteredValue.value = 'filtered-company-id';
  createMessage.success('已设置默认值');
}

function showAllValues() {
  showValues.value = !showValues.value;
}
</script>

<style scoped>
.company-select-test {
  padding: 20px;
}

.result-display {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
}

h3 {
  margin-bottom: 12px;
  color: #1890ff;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
