<template>
  <div class="company-form-example">
    <a-card title="表单中使用单位选择组件示例">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        
        <!-- 基础信息 -->
        <a-divider>基础信息</a-divider>
        
        <a-form-item label="姓名" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入姓名" />
        </a-form-item>

        <a-form-item label="所属单位" name="companyId">
          <JSelectCompany
            v-model:value="formData.companyId"
            placeholder="请选择所属单位"
            :leaf-only="true"
            @change="handleCompanyChange"
          />
        </a-form-item>

        <a-form-item label="管理单位" name="managedCompanies">
          <JSelectCompany
            v-model:value="formData.managedCompanies"
            :multiple="true"
            placeholder="请选择管理的单位"
            @change="handleManagedCompaniesChange"
          />
        </a-form-item>

        <a-form-item label="合作单位" name="partnerCompany">
          <JSelectCompany
            v-model:value="formData.partnerCompany"
            :label-in-value="true"
            placeholder="请选择合作单位"
            @change="handlePartnerCompanyChange"
          />
        </a-form-item>

        <!-- 高级选项 -->
        <a-divider>高级选项</a-divider>

        <a-form-item label="授权单位" name="authorizedCompanies">
          <JSelectCompany
            v-model:value="formData.authorizedCompanies"
            :multiple="true"
            :show-category="false"
            placeholder="请选择授权单位（不显示分类）"
          />
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea 
            v-model:value="formData.remark" 
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit">提交</a-button>
            <a-button @click="handleReset">重置</a-button>
            <a-button @click="handleFillDemo">填充示例数据</a-button>
          </a-space>
        </a-form-item>

      </a-form>

      <!-- 表单数据预览 -->
      <a-divider>表单数据预览</a-divider>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>

    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { Form } from 'ant-design-vue';
import JSelectCompany from '/@/components/Form/src/jeecg/components/JSelectCompany.vue';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();
const useForm = Form.useForm;

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  name: '',
  companyId: undefined,
  managedCompanies: [],
  partnerCompany: undefined,
  authorizedCompanies: [],
  remark: '',
});

// 表单验证规则
const formRules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在2-50个字符', trigger: 'blur' }
  ],
  companyId: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ],
  managedCompanies: [
    { type: 'array', min: 1, message: '请至少选择一个管理单位', trigger: 'change' }
  ],
});

// 使用表单验证
const { validate, resetFields } = useForm(formData, formRules);

// 事件处理函数
function handleCompanyChange(value: any, label?: any, extra?: any) {
  console.log('所属单位变化:', { value, label, extra });
  createMessage.info(`选择所属单位: ${label || value}`);
}

function handleManagedCompaniesChange(value: any, label?: any, extra?: any) {
  console.log('管理单位变化:', { value, label, extra });
  const count = Array.isArray(value) ? value.length : 0;
  createMessage.info(`选择了 ${count} 个管理单位`);
}

function handlePartnerCompanyChange(value: any, label?: any, extra?: any) {
  console.log('合作单位变化:', { value, label, extra });
  if (value && typeof value === 'object' && value.label) {
    createMessage.info(`选择合作单位: ${value.label}`);
  }
}

// 表单操作
async function handleSubmit() {
  try {
    await validate();
    console.log('表单数据:', formData);
    createMessage.success('表单验证通过，数据已打印到控制台');
    
    // 这里可以调用API提交数据
    // await submitFormData(formData);
    
  } catch (error) {
    console.error('表单验证失败:', error);
    createMessage.error('表单验证失败，请检查输入');
  }
}

function handleReset() {
  resetFields();
  createMessage.info('表单已重置');
}

function handleFillDemo() {
  // 填充示例数据（需要根据实际的单位ID调整）
  Object.assign(formData, {
    name: '张三',
    companyId: 'demo-company-1',
    managedCompanies: ['demo-company-2', 'demo-company-3'],
    partnerCompany: { 
      value: 'demo-company-4', 
      label: '示例合作单位' 
    },
    authorizedCompanies: ['demo-company-5'],
    remark: '这是一个示例表单，展示了如何在表单中使用单位选择组件。',
  });
  createMessage.success('已填充示例数据');
}
</script>

<style scoped>
.company-form-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.ant-divider {
  margin: 24px 0 16px 0;
}
</style>
