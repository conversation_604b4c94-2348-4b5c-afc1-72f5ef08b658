<template>
  <div class="dict-default-value-test">
    <h2>字典默认值功能测试</h2>
    
    <a-divider>系统字典表测试（sys_dict_item）</a-divider>
    
    <a-form :model="formData" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="性别（自动设置默认值）">
            <JDictSelectTag
              v-model:value="formData.gender1"
              dictCode="sex"
              :autoSetDefault="true"
              placeholder="请选择性别"
            />
            <div class="value-display">当前值: {{ formData.gender1 }}</div>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="性别（不自动设置默认值）">
            <JDictSelectTag
              v-model:value="formData.gender2"
              dictCode="sex"
              :autoSetDefault="false"
              placeholder="请选择性别"
            />
            <div class="value-display">当前值: {{ formData.gender2 }}</div>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="性别（Radio模式，自动默认值）">
            <JDictSelectTag
              v-model:value="formData.gender3"
              dictCode="sex"
              type="radio"
              :autoSetDefault="true"
              placeholder="请选择性别"
            />
            <div class="value-display">当前值: {{ formData.gender3 }}</div>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-divider>表字典测试</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="用户（通过defaultField）">
            <JDictSelectTag
              v-model:value="formData.user1"
              dictCode="sys_user,realname,id"
              :autoSetDefault="true"
              defaultField="is_default"
              placeholder="请选择用户"
            />
            <div class="value-display">当前值: {{ formData.user1 }}</div>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="用户（通过defaultIndex）">
            <JDictSelectTag
              v-model:value="formData.user2"
              dictCode="sys_user,realname,id"
              :autoSetDefault="true"
              :defaultIndex="0"
              placeholder="请选择用户"
            />
            <div class="value-display">当前值: {{ formData.user2 }}</div>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="用户（指定defaultValue）">
            <JDictSelectTag
              v-model:value="formData.user3"
              dictCode="sys_user,realname,id"
              :autoSetDefault="true"
              defaultValue="admin"
              placeholder="请选择用户"
            />
            <div class="value-display">当前值: {{ formData.user3 }}</div>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-divider>多选模式测试</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="多选字典（自动默认值）">
            <JDictSelectTag
              v-model:value="formData.multiSelect1"
              dictCode="sex"
              mode="multiple"
              :autoSetDefault="true"
              placeholder="请选择"
            />
            <div class="value-display">当前值: {{ JSON.stringify(formData.multiSelect1) }}</div>
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="多选字典（指定多个默认值）">
            <JDictSelectTag
              v-model:value="formData.multiSelect2"
              dictCode="sex"
              mode="multiple"
              :autoSetDefault="true"
              :defaultValue="['1', '2']"
              placeholder="请选择"
            />
            <div class="value-display">当前值: {{ JSON.stringify(formData.multiSelect2) }}</div>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-divider>RadioButton模式测试</a-divider>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="状态（RadioButton模式）">
            <JDictSelectTag
              v-model:value="formData.status1"
              dictCode="dict_item_status"
              type="radioButton"
              :autoSetDefault="true"
              placeholder="请选择状态"
            />
            <div class="value-display">当前值: {{ formData.status1 }}</div>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="状态（指定默认值）">
            <JDictSelectTag
              v-model:value="formData.status2"
              dictCode="dict_item_status"
              type="radioButton"
              :autoSetDefault="true"
              defaultValue="1"
              placeholder="请选择状态"
            />
            <div class="value-display">当前值: {{ formData.status2 }}</div>
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider>边界情况测试</a-divider>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="已有值（不覆盖）">
            <JDictSelectTag
              v-model:value="formData.existingValue"
              dictCode="sex"
              :autoSetDefault="true"
              :onlySetWhenEmpty="true"
              placeholder="请选择"
            />
            <div class="value-display">当前值: {{ formData.existingValue }}</div>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item label="已有值（强制覆盖）">
            <JDictSelectTag
              v-model:value="formData.forceOverride"
              dictCode="sex"
              :autoSetDefault="true"
              :onlySetWhenEmpty="false"
              placeholder="请选择"
            />
            <div class="value-display">当前值: {{ formData.forceOverride }}</div>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item label="无默认值字典">
            <JDictSelectTag
              v-model:value="formData.noDefault"
              dictCode="priority"
              :autoSetDefault="true"
              placeholder="请选择优先级"
            />
            <div class="value-display">当前值: {{ formData.noDefault }}</div>
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider>控制按钮</a-divider>

      <a-space>
        <a-button @click="resetForm">重置表单</a-button>
        <a-button type="primary" @click="showFormData">显示表单数据</a-button>
        <a-button @click="setFormData">设置表单数据</a-button>
        <a-button @click="testBoundaryCase">测试边界情况</a-button>
        <a-button @click="runAllTests">运行所有测试</a-button>
      </a-space>
    </a-form>
    
    <a-divider>测试结果</a-divider>
    <div v-if="testResults.length > 0">
      <a-list :data-source="testResults" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <span :class="item.passed ? 'test-passed' : 'test-failed'">
                  {{ item.passed ? '✓' : '✗' }} {{ item.name }}
                </span>
              </template>
              <template #description>
                {{ item.details }}
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </div>

    <a-divider>表单数据</a-divider>
    <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';

const formData = reactive({
  gender1: null,
  gender2: null,
  gender3: null,
  user1: null,
  user2: null,
  user3: null,
  multiSelect1: [],
  multiSelect2: [],
  status1: null,
  status2: null,
  existingValue: '2', // 预设值，测试不覆盖
  forceOverride: '2', // 预设值，测试强制覆盖
  noDefault: null,
});

// 测试结果记录
const testResults = ref([]);

function resetForm() {
  Object.keys(formData).forEach(key => {
    if (Array.isArray(formData[key])) {
      formData[key] = [];
    } else {
      formData[key] = null;
    }
  });
  message.success('表单已重置');
}

function showFormData() {
  console.log('表单数据:', formData);
  message.info('表单数据已输出到控制台');
}

function setFormData() {
  formData.gender1 = '1';
  formData.gender2 = '2';
  formData.user1 = 'admin';
  formData.multiSelect1 = ['1'];
  message.success('表单数据已设置');
}

function testBoundaryCase() {
  // 重置边界测试用例
  formData.existingValue = '2';
  formData.forceOverride = '2';
  formData.noDefault = null;

  message.info('边界情况测试数据已重置');
}

function runAllTests() {
  testResults.value = [];

  // 测试1: 系统字典默认值
  const test1 = {
    name: '系统字典默认值测试',
    passed: formData.gender1 !== null,
    details: `gender1 = ${formData.gender1}`
  };
  testResults.value.push(test1);

  // 测试2: 多选默认值
  const test2 = {
    name: '多选默认值测试',
    passed: Array.isArray(formData.multiSelect2) && formData.multiSelect2.length > 0,
    details: `multiSelect2 = ${JSON.stringify(formData.multiSelect2)}`
  };
  testResults.value.push(test2);

  // 测试3: 边界情况测试
  const test3 = {
    name: '不覆盖已有值测试',
    passed: formData.existingValue === '2',
    details: `existingValue = ${formData.existingValue} (应该保持为'2')`
  };
  testResults.value.push(test3);

  const passedCount = testResults.value.filter(t => t.passed).length;
  const totalCount = testResults.value.length;

  if (passedCount === totalCount) {
    message.success(`所有测试通过 (${passedCount}/${totalCount})`);
  } else {
    message.warning(`部分测试失败 (${passedCount}/${totalCount})`);
  }

  console.log('测试结果:', testResults.value);
}
</script>

<style scoped>
.dict-default-value-test {
  padding: 20px;
}

.value-display {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.test-passed {
  color: #52c41a;
  font-weight: bold;
}

.test-failed {
  color: #ff4d4f;
  font-weight: bold;
}
</style>
