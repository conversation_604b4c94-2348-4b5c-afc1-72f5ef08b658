<template>
  <tr
    :class="[
      'conclusion-row',
      {
        editing: record.editable,
        'main-factor': record.mainFlag === '1',
        'new-record': record.isNew,
        saving: saving,
      },
    ]"
  >
    <!-- 选择列 -->
    <td class="text-center">
      <a-checkbox v-if="!record.editable && !record.isNew" :checked="selected" @change="$emit('select', record, $event.target.checked)" />
    </td>

    <!-- 序号列 -->
    <td class="text-center">
      <span class="row-index">{{ index + 1 }}</span>
    </td>

    <!-- 主要/次要列 -->
    <td class="text-center">
      <template v-if="record.editable">
        <a-radio-group v-model:value="record.mainFlag" button-style="solid" size="small">
          <a-radio-button value="1">主要</a-radio-button>
          <a-radio-button value="0">次要</a-radio-button>
        </a-radio-group>
      </template>
      <template v-else>
        <a-tag :color="record.mainFlag === '1' ? 'red' : 'blue'" size="small">
          {{ record.mainFlag === '1' ? '主要' : '次要' }}
        </a-tag>
      </template>
    </td>

    <!-- 危害因素列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JAsyncSearchSelect
          v-model:value="record.riskCode"
          dict="zy_risk_factor,name,code"
          placeholder="请选择危害因素"
          size="small"
          style="width: 100%"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <span class="field-text">{{ record.riskFactorText || record.riskFactor || '-' }}</span>
      </template>
    </td>

    <!-- 结论列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.conclusion"
          dictCode="zy_conclusion_dict,dict_text,code"
          placeholder="请选择结论"
          size="small"
          style="width: 100%"
          :class="{ 'required-field': !record.conclusion }"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <a-tag v-if="record.conclusion" color="green" size="small">
          {{ record.conclusionText || record.conclusion }}
        </a-tag>
        <span v-else class="empty-field">-</span>
      </template>
    </td>

    <!-- 职业病列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.zyDisease"
          dictCode="zy_disease_dict,dict_text,code"
          placeholder="请选择职业病"
          :triggerChange="false"
          size="small"
          style="width: 100%"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <div v-if="record.zyDisease" class="tag-container">
          <template v-if="record.zyDiseaseText">
            <a-tag v-for="diseaseText in record.zyDiseaseText.split('，')" :key="diseaseText" size="small" color="orange">
              {{ diseaseText }}
            </a-tag>
          </template>
          <template v-else>
            <a-tag v-for="disease in (record.zyDisease || '').split(',')" :key="disease" size="small" color="orange">
              {{ disease }}
            </a-tag>
          </template>
        </div>
        <span v-else class="empty-field">-</span>
      </template>
    </td>

    <!-- 禁忌证列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.zySymptom"
          dictCode="zy_taboo_symptom,name,code"
          placeholder="请选择禁忌证"
          :triggerChange="false"
          size="small"
          style="width: 100%"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <div v-if="record.zySymptom" class="tag-container">
          <template v-if="record.zySymptomText">
            <a-tag v-for="symptomText in record.zySymptomText.split('，')" :key="symptomText" size="small" color="purple">
              {{ symptomText }}
            </a-tag>
          </template>
          <template v-else>
            <a-tag v-for="symptom in (record.zySymptom || '').split(',')" :key="symptom" size="small" color="purple">
              {{ symptom }}
            </a-tag>
          </template>
        </div>
        <span v-else class="empty-field">-</span>
      </template>
    </td>

    <!-- 结论依据列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.according"
          dictCode="zy_conclusion_according,content,content"
          placeholder="请选择依据"
          size="small"
          style="width: 100%"
          :class="{ 'required-field': !record.according }"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <span class="field-text">{{ record.according || '-' }}</span>
      </template>
    </td>

    <!-- 处理意见列 -->
    <td>
      <template v-if="record.editable">
        <a-textarea v-model:value="record.advice" placeholder="请输入处理意见" :rows="2" size="small" style="width: 100%" />
      </template>
      <template v-else>
        <div class="advice-text" :title="record.advice">
          {{ record.advice || '-' }}
        </div>
      </template>
    </td>

    <!-- 操作列 -->
    <td class="text-center">
      <div class="action-buttons">
        <!-- 非编辑状态：显示编辑和删除按钮 -->
        <template v-if="!record.editable">
          <a-space size="small">
            <a-button type="primary" size="small" @click="$emit('edit', record)" :loading="loading">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>

            <a-popconfirm title="确定要删除这条记录吗？" ok-text="确定" cancel-text="取消" @confirm="$emit('delete', record)" placement="topLeft">
              <a-button type="primary" size="small" danger>
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>

        <!-- 编辑状态：显示保存和取消按钮 -->
        <template v-else>
          <a-space size="small">
            <a-button type="primary" size="small" @click="$emit('save', record)" :loading="saving">
              <template #icon><CheckOutlined /></template>
              保存
            </a-button>

            <a-button size="small" @click="$emit('cancel', record)" :disabled="saving">
              <template #icon><CloseOutlined /></template>
              取消
            </a-button>
          </a-space>
        </template>

        <!-- 保存状态指示器 -->
        <div v-if="saving" class="saving-indicator">
          <a-spin size="small" />
        </div>
      </div>
    </td>
  </tr>
</template>

<script lang="ts" setup>
  import { EditOutlined, CheckOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';

  interface Props {
    record: any;
    index: number;
    selected?: boolean;
    loading?: boolean;
    saving?: boolean;
  }

  interface Emits {
    (e: 'edit', record: any): void;
    (e: 'save', record: any): void;
    (e: 'cancel', record: any): void;
    (e: 'delete', record: any): void;
    (e: 'select', record: any, checked: boolean): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    selected: false,
    loading: false,
    saving: false,
  });

  const emit = defineEmits<Emits>();

  // 获取下拉框容器，确保下拉框显示在正确位置
  const getPopupContainer = (triggerNode: HTMLElement) => {
    // 查找最近的表格容器
    let container = triggerNode.closest('.conclusion-table-container');
    if (!container) {
      container = triggerNode.closest('.table-wrapper');
    }
    if (!container) {
      container = document.body;
    }
    return container as HTMLElement;
  };
</script>

<style lang="less" scoped>
  .conclusion-row {
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      background-color: #e6f7ff !important;

      .action-buttons {
        opacity: 1;
      }
    }

    &.editing {
      background-color: #f6ffed !important;
      box-shadow: inset 0 0 0 1px #b7eb8f;

      td {
        border-bottom-color: #b7eb8f;
      }
    }

    &.new-record {
      background-color: #f0f9ff !important;

      &.editing {
        background-color: #e6fffb !important;
        box-shadow: inset 0 0 0 1px #87e8de;
      }
    }

    &.main-factor {
      border-left: 4px solid #ff4d4f;

      .row-index {
        color: #ff4d4f;
        font-weight: 600;
      }
    }

    &.saving {
      opacity: 0.7;
      pointer-events: none;
    }

    td {
      padding: 12px 8px;
      border-bottom: 1px solid #f0f0f0;
      vertical-align: middle;
      font-size: 13px;

      &.text-center {
        text-align: center;
      }

      // 下拉框单元格特殊处理
      &.dropdown-cell {
        position: relative;
        z-index: 1;

        // 编辑状态时提升z-index
        .conclusion-row.editing & {
          z-index: 100;
        }
      }

      // 表单控件样式优化
      :deep(.ant-select),
      :deep(.ant-input),
      :deep(.ant-textarea) {
        font-size: 13px;
      }

      :deep(.ant-radio-group) {
        .ant-radio-button-wrapper {
          font-size: 12px;
          height: 28px;
          line-height: 26px;
          padding: 0 8px;
        }
      }
    }
  }

  .row-index {
    font-weight: 500;
    color: #666;
    font-size: 14px;
  }

  .status-indicators {
    margin-top: 4px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;

    .ant-tag {
      margin: 0;
      font-size: 10px;
      padding: 0 4px;
      line-height: 16px;
    }
  }

  .field-text {
    color: #262626;
    font-size: 13px;
    line-height: 1.4;
  }

  .empty-field {
    color: #bfbfbf;
    font-style: italic;
    font-size: 12px;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;

    .ant-tag {
      margin: 0;
      font-size: 11px;
      padding: 0 6px;
      line-height: 18px;
    }
  }

  .advice-text {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    color: #262626;
    line-height: 1.4;
    cursor: help;

    &:hover {
      color: #1890ff;
    }
  }

  .action-buttons {
    position: relative;
    opacity: 0.8;
    transition: opacity 0.2s ease;

    .ant-btn {
      font-size: 12px;
      height: 28px;
      padding: 0 8px;

      &.ant-btn-sm {
        height: 24px;
        padding: 0 6px;
        font-size: 11px;
      }
    }

    .saving-indicator {
      position: absolute;
      top: -25px;
      right: 0;
      font-size: 11px;
      color: #1890ff;
      background: white;
      padding: 2px 6px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .required-field {
    :deep(.ant-select-selector) {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }

    :deep(.ant-select-focused .ant-select-selector) {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .conclusion-row td {
      padding: 8px 4px;
      font-size: 12px;
    }

    .advice-text {
      max-width: 120px;
    }

    .action-buttons .ant-btn {
      font-size: 11px;
      height: 24px;
      padding: 0 4px;
    }
  }

  @media (max-width: 768px) {
    .conclusion-row td {
      padding: 6px 2px;
      font-size: 11px;
    }

    .tag-container .ant-tag {
      font-size: 10px;
      padding: 0 4px;
      line-height: 16px;
    }

    .status-indicators .ant-tag {
      font-size: 9px;
      padding: 0 3px;
      line-height: 14px;
    }
  }
</style>
